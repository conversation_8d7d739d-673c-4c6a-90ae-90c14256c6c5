"use client";

import React, { createContext, useContext, useState } from 'react';

const ModalContext = createContext();

export const useModal = () => {
  const context = useContext(ModalContext);
  if (!context) {
    throw new Error('useModal must be used within a ModalProvider');
  }
  return context;
};

export const ModalProvider = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [modalType, setModalType] = useState(null);
  const [modalData, setModalData] = useState(null);

  const openModal = (type, data = null) => {
    setModalType(type);
    setModalData(data);
    setIsOpen(true);
    // Prevent body scroll when modal is open
    document.body.style.overflow = 'hidden';
  };

  const closeModal = () => {
    setIsOpen(false);
    // Restore body scroll
    document.body.style.overflow = 'unset';

    // Delay clearing modal type/data until after animation completes
    setTimeout(() => {
      setModalType(null);
      setModalData(null);
    }, 250); // Slightly longer than the 200ms animation
  };

  return (
    <ModalContext.Provider value={{
      isOpen,
      modalType,
      modalData,
      openModal,
      closeModal
    }}>
      {children}
    </ModalContext.Provider>
  );
};
