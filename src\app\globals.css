@import "tailwindcss";

@theme {
  /* Your custom colors - now theme-aware */
  --color-primary: var(--color-primary);
  --color-secondary: var(--color-secondary);
  --color-accent: #f26430;     /* Your accent color stays the same */

  /* Your custom fonts */
  --font-sans: var(--font-open-sans);
  --font-heading: var(--font-poppins);
}

html {
  scroll-behavior: smooth;
}

html, body {
  height: 100%;
  height: 100dvh;
}

/* Default theme variables (dark mode) */
:root {
  --color-primary: #0b0505;       /* Dark background */
  --color-secondary: #ffead6;     /* Light text */
  --color-background: #0b0505;    /* Dark background */
  --color-foreground: #ffead6;    /* Light text */
  --color-accent: #f26430;        /* Your accent color */
  --color-primary-rgb: 11, 5, 5;
  --font-sans: var(--font-open-sans);
  --font-heading: var(--font-poppins);
}

/* Light mode theme */
:root[data-theme="light"] {
  --color-primary: #ffffff;       /* Light background (was secondary) */
  --color-secondary: #0b0505;     /* Dark text (was primary) */
  --color-background: #ffffff;    /* Light background */
  --color-foreground: #0b0505;    /* Dark text */
  --color-accent: #f26430;        /* Your accent color stays the same */
  --color-primary-rgb: 255, 234, 214;
  --font-sans: var(--font-open-sans);
  --font-heading: var(--font-poppins);
}

/* Dark mode theme (explicit) */
:root[data-theme="dark"] {
  --color-primary: #0b0505;       /* Dark background */
  --color-secondary: #ffead6;     /* Light text */
  --color-background: #0b0505;    /* Dark background */
  --color-foreground: #ffead6;    /* Light text */
  --color-accent: #f26430;        /* Your accent color stays the same */
  --color-primary-rgb: 11, 5, 5;
  --font-sans: var(--font-open-sans);
  --font-heading: var(--font-poppins);
}

body {
  background: var(--color-background);
  color: var(--color-foreground);
  font-family: var(--font-sans);
}

.scroll-down-arrow {
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Custom Scrollbar Styling */

/* Webkit browsers - Simple approach that actually works */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 6px;
  margin: 2px; /* Padding from edges */
}

/* Dark mode - light scrollbar */
::-webkit-scrollbar-thumb {
  background: rgba(255, 234, 214, 0.3);
  border-radius: 6px;
  border: 2px solid transparent;
  background-clip: padding-box;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(242, 100, 48, 0.8);
  background-clip: padding-box;
}

/* Light mode - dark scrollbar */
:root[data-theme="light"] ::-webkit-scrollbar-thumb {
  background: rgba(11, 5, 5, 0.4);
  background-clip: padding-box;
}

:root[data-theme="light"] ::-webkit-scrollbar-thumb:hover {
  background: rgba(242, 100, 48, 0.8);
  background-clip: padding-box;
}

/* Firefox - Force with multiple approaches */
html {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 234, 214, 0.3) transparent;
}

/* Light mode - FORCE dark scrollbar in Firefox */
:root[data-theme="light"] html {
  scrollbar-color: #0b0505 transparent !important;
}

/* Alternative Firefox targeting */
@-moz-document url-prefix() {
  html {
    scrollbar-color: rgba(255, 234, 214, 0.3) transparent !important;
  }

  :root[data-theme="light"] html {
    scrollbar-color: #0b0505 transparent !important;
  }
}

/* Even more specific Firefox targeting */
@supports (-moz-appearance: none) {
  html {
    scrollbar-color: rgba(255, 234, 214, 0.3) transparent !important;
  }

  :root[data-theme="light"] html {
    scrollbar-color: #0b0505 transparent !important;
  }
}

/* Modal scroll prevention without layout shift */
body.modal-open {
  overflow: hidden;
  padding-right: var(--scrollbar-width, 0px);
}

/* Modal content scrollbar styling */
.modal-content::-webkit-scrollbar {
  width: 12px;
}

.modal-content::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 6px;
  margin: 2px; /* Padding from edges */
}

/* Modal scrollbar - same as main page */
.modal-content::-webkit-scrollbar-thumb {
  background: rgba(255, 234, 214, 0.2);
  border-radius: 6px;
  border: 2px solid transparent;
  background-clip: padding-box;
  transition: background 0.2s ease;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: rgba(242, 100, 48, 0.8);
  background-clip: padding-box;
}

/* Light mode modal scrollbar */
:root[data-theme="light"] .modal-content::-webkit-scrollbar-thumb {
  background: rgba(11, 5, 5, 0.3);
  background-clip: padding-box;
}

:root[data-theme="light"] .modal-content::-webkit-scrollbar-thumb:hover {
  background: rgba(242, 100, 48, 0.8);
  background-clip: padding-box;
}

/* Safari mobile viewport fix */
.h-screen {
  height: 100vh;
  height: 100dvh;
}

.min-h-screen {
  min-height: 100vh;
  min-height: 100dvh;
}

/* Navbar hover effects */
.navbar-item {
  position: relative;
  transition: color 0.3s ease;
}

.navbar-item::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: var(--color-accent);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.navbar-item:hover::after {
  width: 100%;
}

.navbar-item:hover {
  color: var(--color-accent);
}

/* Button hover effect - simple fade version with border */
.request-project-btn {
  transition: all 0.1s ease;
}

.request-project-btn:hover {
  background-color: var(--color-accent) !important;
  color: var(--color-primary);
}
